import { useEffect, useRef } from 'react';
import { useRouter, usePathname } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import useProfileData from './useProfileData';

/**
 * Hook that automatically redirects authenticated users to profile creation
 * when they have no profiles, unless they're already on the profile creation page
 */
export const useProfileRedirect = () => {
  const { user } = useAuth();
  const { profiles, isLoading } = useProfileData();
  const router = useRouter();
  const pathname = usePathname();
  const hasRedirected = useRef(false);

  useEffect(() => {
    // Don't redirect if:
    // - User is not authenticated
    // - Profile data is still loading
    // - Already redirected in this session
    // - Already on profile creation page or related pages
    if (!user || isLoading || hasRedirected.current) {
      return;
    }

    // Don't redirect if already on profile-related pages
    const isOnProfilePages = pathname?.includes('/profiles/add') || 
                            pathname?.includes('/profiles/') ||
                            pathname?.includes('/(onboarding)') ||
                            pathname?.includes('/(auth)');
    
    if (isOnProfilePages) {
      return;
    }

    // Check if user has no profiles
    if (profiles.length === 0) {
      console.log('PROFILE REDIRECT: User has no profiles, redirecting to profile creation');
      hasRedirected.current = true;
      router.replace('/profiles/add');
    }
  }, [user, profiles, isLoading, pathname, router]);

  // Reset redirect flag when profiles are added
  useEffect(() => {
    if (profiles.length > 0 && hasRedirected.current) {
      hasRedirected.current = false;
    }
  }, [profiles.length]);

  return {
    shouldRedirect: user && !isLoading && profiles.length === 0,
    isLoading,
    profileCount: profiles.length
  };
};
