import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import SizeModal from './SizeModal';

interface SizeOption {
  label: string;
  value: string;
  description: string;
  category: string;
  isCommon: boolean;
  icon: string;
}

interface SizeGridProps {
  options: SizeOption[];
  selectedValue?: string;
  onValueChange: (value: string) => void;
  label?: string;
  error?: string;
  accessibilityLabel?: string;
  sizeType?: 'clothing' | 'shoe';
}

const SizeGrid: React.FC<SizeGridProps> = ({
  options,
  selectedValue,
  onValueChange,
  label,
  error,
  accessibilityLabel,
  sizeType = 'clothing'
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Theme colors
  const themedColors = {
    primary: isDark ? '#D96D00' : '#A3002B',
    textPrimary: isDark ? '#F9FAFB' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    border: isDark ? '#374151' : '#E5E7EB',
    card: isDark ? '#1F2937' : '#FFFFFF',
    background: isDark ? '#111827' : '#F9FAFB',
    error: isDark ? '#F87171' : '#EF4444',
    commonBadge: isDark ? '#374151' : '#F3F4F6',
  };

  // Find selected size option
  const selectedOption = options.find(option => option.value === selectedValue);

  const openModal = () => {
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const handleSizeSelect = (value: string) => {
    onValueChange(value);
    closeModal();
  };

  // Placeholder text based on size type
  const getPlaceholderText = () => {
    if (sizeType === 'clothing') {
      return 'Select Clothing Size';
    }
    return 'Select Shoe Size';
  };

  return (
    <View className="mb-4">
      {/* Label */}
      {label && (
        <Text
          className="mb-3 text-sm font-medium"
          style={{ color: themedColors.textPrimary }}
        >
          {label}
        </Text>
      )}

      {/* Trigger Button */}
      <TouchableOpacity
        onPress={openModal}
        className="flex-row justify-between items-center p-4 mt-2 rounded-xl border border-border dark:bg-card-dark"
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel || `Select ${sizeType} size`}
      >
        <View className="flex-row flex-1 items-center">
          {/* Selected Size Preview */}
          {selectedOption ? (
            <>
              <View
                className="p-2 mr-3 rounded-full"
                style={{
                  backgroundColor: themedColors.background,
                }}
              >
                <Feather
                  name={selectedOption.icon as any}
                  size={18}
                  color={themedColors.primary}
                />
              </View>
              <View className="flex-1">
                <View className="flex-row items-center">
                  <Text
                    className="mr-2 text-base font-medium"
                    style={{ color: themedColors.textPrimary }}
                  >
                    {selectedOption.label}
                  </Text>
                  
                  {/* Popular Badge */}
                  {selectedOption.isCommon && (
                    <View
                      className="px-2 py-1 rounded-full"
                      style={{ backgroundColor: themedColors.commonBadge }}
                    >
                      <Text
                        className="text-xs font-medium"
                        style={{ color: themedColors.textSecondary }}
                      >
                        Popular
                      </Text>
                    </View>
                  )}
                </View>
                <Text
                  className="mt-1 text-sm"
                  style={{ color: themedColors.textSecondary }}
                  numberOfLines={1}
                >
                  {selectedOption.description}
                </Text>
              </View>
            </>
          ) : (
            <View className="flex-row items-center">
              <Text
                className="text-base"
                style={{ color: themedColors.textSecondary }}
              >
                {getPlaceholderText()}
              </Text>
            </View>
          )}
        </View>

        {/* Chevron Icon */}
        <Feather
          name="chevron-down"
          size={20}
          color={themedColors.textSecondary}
        />
      </TouchableOpacity>

      {/* Size Modal */}
      <SizeModal
        isVisible={isModalVisible}
        onClose={closeModal}
        options={options}
        selectedValue={selectedValue}
        onValueChange={handleSizeSelect}
        title={label || `Choose ${sizeType === 'clothing' ? 'Clothing' : 'Shoe'} Size`}
        sizeType={sizeType}
      />

      {/* Error Message */}
      {error && (
        <Text
          className="mt-2 text-sm"
          style={{ color: themedColors.error }}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

export default SizeGrid; 