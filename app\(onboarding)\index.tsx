import React, { useRef } from "react";
import { View, FlatList, Animated, StyleSheet, useWindowDimensions } from "react-native";
import { useRouter } from "expo-router";
import OnboardingSlide from "../../components/onboarding/OnboardingSlide";
import PaginationDots from "../../components/onboarding/PaginationDots";
import onboarding1 from "../../assets/images/onboarding1.png";
import onboarding2 from "../../assets/images/onboarding2.png";
import onboarding3 from "../../assets/images/onboarding3.png";
import { useColorScheme } from "nativewind";

const onboardingSlides = [
  {
    id: 1,
    image: onboarding1, 
    title: "What if you always gave the perfect gift?",
    subtitle: "Welcome to Aril, the solution to the stress of gift-giving.",
  },
  {
    id: 2,
    image: onboarding2,
    title: "Capture Every Important Hint",
    subtitle: "Easily save preferences, wish lists, and random ideas in one private space.",
  },
  {
    id: 3,
    image: onboarding3,
    title: "Stay Ahead of Special Dates",
    subtitle: "Get timely reminders for birthdays and other important dates, with smart gift suggestions.",
  }
];

const OnboardingScreen: React.FC = () => {
  const router = useRouter();
  const { width } = useWindowDimensions();
  const scrollX = useRef(new Animated.Value(0)).current;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const themedColors = {
      background: isDark ? '#111827' : '#F9FAFB',
  };

  const handleGetStarted = () => router.push("/(auth)/signup");
  const handleLogin = () => router.push("/(auth)/login");

  return (
    <View style={[styles.container, { backgroundColor: themedColors.background }]}>
      <FlatList
        data={onboardingSlides}
        renderItem={({ item }) => (
          <OnboardingSlide 
            item={item} 
            onGetStarted={handleGetStarted}
            onLogin={handleLogin}
          />
        )}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
        keyExtractor={(item) => item.id.toString()}
      />
      <PaginationDots data={onboardingSlides} scrollX={scrollX} screenWidth={width} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default OnboardingScreen; 