import React from 'react';
import { View, Text } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import ColorGrid from '@/components/ui/ColorGrid';
import StyleGrid from '@/components/ui/StyleGrid';
import { TagInput } from '@/components/ui/TagInput';
import Input from '@/components/ui/Input'; // Import the Input component
import { brandSuggestions } from '@/constants/suggestions';
import { colorOptions, styleOptions } from '@/constants/profileOptions'; // Import options from new file
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm
import { Feather } from '@expo/vector-icons';

interface PreferencesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

const PreferencesSection: React.FC<PreferencesSectionProps> = ({
  control,
  errors,
}) => {
  return (
    <View className="gap-3">
      {/* Favorite Color */}
      <View>
        <View className="flex-row gap-2 items-center mb-1">
          <View className="p-2 bg-red-50 rounded-lg">
            <Feather name="droplet" size={20} color="#DC2626" />
          </View>
          <Text className="text-sm font-medium dark:text-text-primary-dark text-muted-foreground">Favorite Color</Text>
        </View>
        <Controller
          control={control}
          name="preferences.favoriteColor"
          render={({ field: { onChange, value } }) => (
            <ColorGrid
              options={colorOptions}
              selectedValue={value}
              onValueChange={onChange}
              error={errors.preferences?.favoriteColor?.message}
              accessibilityLabel="Select favorite color grid"
            />
          )}
        />
      </View>
      {/* Preferred Style */}
      <View>
        <View className="flex-row gap-2 items-center mb-1">
          <View className="p-2 bg-red-50 rounded-lg">
            <Feather name="sliders" size={20} color="#DC2626" />
          </View>
          <Text className="text-sm font-medium dark:text-text-primary-dark text-muted-foreground">Preferred Style</Text>
        </View>
        <Controller
          control={control}
          name="preferences.preferredStyle"
          render={({ field: { onChange, value } }) => (
            <StyleGrid
              options={styleOptions}
              selectedValue={value}
              onValueChange={onChange}
              error={errors.preferences?.preferredStyle?.message}
              accessibilityLabel="Select preferred style grid"
            />
          )}
        />
      </View>
      {/* Favorite Brands */}
      <View>
        <View className="flex-row gap-2 items-center mb-1">
          <View className="p-2 bg-red-50 rounded-lg">
            <Feather name="tag" size={20} color="#DC2626" />
          </View>
          <Text className="text-sm font-medium dark:text-text-primary-dark text-muted-foreground">Favorite Brands</Text>
        </View>
        <Controller
          control={control}
          name="preferences.favoriteBrands"
          render={({ field: { onChange, value } }) => (
            <TagInput
              tags={value || []}
              onChangeTags={onChange}
              placeholder="e.g., Nike, Apple, Moleskine"
              error={errors.preferences?.favoriteBrands?.message}
              accessibilityLabel="Favorite brands input"
              suggestions={brandSuggestions}
            />
          )}
        />
      </View>
      {/* Minimum Budget */}
      <View>
        <View className="flex-row gap-2 items-center mb-1">
          <View className="p-2 bg-red-50 rounded-lg">
            <Feather name="arrow-down-circle" size={20} color="#DC2626" />
          </View>
          <Text className="text-sm font-medium dark:text-text-primary-dark text-muted-foreground">Minimum Budget (Optional)</Text>
        </View>
        <Controller
          control={control}
          name="preferences.budgetMin"
          render={({ field: { onChange, value } }) => (
            <Input
              placeholder="e.g., 50"
              keyboardType="numeric"
              onChangeText={onChange}
              value={value?.toString()}
              error={errors.preferences?.budgetMin?.message}
              accessibilityLabel="Minimum budget input"
            />
          )}
        />
      </View>
      {/* Maximum Budget */}
      <View>
        <View className="flex-row gap-2 items-center mb-1">
          <View className="p-2 bg-red-50 rounded-lg">
            <Feather name="arrow-up-circle" size={20} color="#DC2626" />
          </View>
          <Text className="text-sm font-medium dark:text-text-primary-dark text-muted-foreground">Maximum Budget (Optional)</Text>
        </View>
        <Controller
          control={control}
          name="preferences.budgetMax"
          render={({ field: { onChange, value } }) => (
            <Input
              placeholder="e.g., 200"
              keyboardType="numeric"
              onChangeText={onChange}
              value={value?.toString()}
              error={errors.preferences?.budgetMax?.message}
              accessibilityLabel="Maximum budget input"
            />
          )}
        />
      </View>
    </View>
  );
};

export default PreferencesSection;