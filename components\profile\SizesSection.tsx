import React from 'react';
import { View, Text } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import SizeGrid from '@/components/ui/SizeGrid';
import { clothingSizeOptions, shoeSizeOptions } from '@/constants/profileOptions';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm
import { Feather } from '@expo/vector-icons';

interface SizesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

const SizesSection: React.FC<SizesSectionProps> = ({
  control,
  errors,
}) => {
  return (
    <View className="gap-3">
      {/* Clothing Size */}
      <View>
        <View className="flex-row gap-2 items-center mb-1">
          <View className="p-2 bg-red-50 rounded-lg">
            <Feather name="user" size={20} color="#DC2626" />
          </View>
          <Text className="text-sm font-medium dark:text-text-primary-dark text-muted-foreground">Clothing Size</Text>
        </View>
        <Controller
          control={control}
          name="clothingSize"
          render={({ field: { onChange, value } }) => (
            <SizeGrid
              options={clothingSizeOptions}
              selectedValue={value}
              onValueChange={onChange}
              error={errors.clothingSize?.message}
              accessibilityLabel="Select clothing size grid"
              sizeType="clothing"
            />
          )}
        />
      </View>
      {/* Shoe Size */}
      <View>
        <View className="flex-row gap-2 items-center mb-1">
          <View className="p-2 bg-red-50 rounded-lg">
            <Feather name="circle" size={20} color="#DC2626" />
          </View>
          <Text className="text-sm font-medium dark:text-text-primary-dark text-muted-foreground">Shoe Size</Text>
        </View>
        <Controller
          control={control}
          name="shoeSize"
          render={({ field: { onChange, value } }) => (
            <SizeGrid
              options={shoeSizeOptions}
              selectedValue={value}
              onValueChange={onChange}
              error={errors.shoeSize?.message}
              accessibilityLabel="Select shoe size grid"
              sizeType="shoe"
            />
          )}
        />
      </View>
    </View>
  );
};

export default SizesSection;